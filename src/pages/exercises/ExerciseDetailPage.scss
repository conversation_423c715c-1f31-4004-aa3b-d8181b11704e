// 动作详情页面样式 - iOS优化版本
.exercise-detail-page {
  height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整体页面滚动

  // iOS Safe Area支持
  @supports (-webkit-touch-callout: none) {
    height: -webkit-fill-available;
  }
}

// 固定头部 - iOS优化版本
.exercise-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  padding-top: calc(env(safe-area-inset-top) + var(--space-4));
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed); // 1030
  flex-shrink: 0;
  min-height: calc(env(safe-area-inset-top) + var(--space-11) + var(--space-8));

  // iOS毛玻璃效果
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);

  // 滚动时的样式变化
  &.scrolled {
    background: rgba(var(--bg-primary-rgb, 255, 255, 255), 0.95);
    box-shadow: var(--shadow-sm);
  }

  .back-btn {
    width: var(--space-11); // 44px - iOS触摸目标标准
    height: var(--space-11);
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);

    svg {
      width: 20px;
      height: 20px;
      color: var(--text-primary);
      stroke-width: 2;
    }

    &:hover {
      background: var(--bg-hover);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // 粘性标题 - 增大字体并居中
  .sticky-title {
    flex: 1;
    text-align: center;
    margin: 0 var(--space-4);

    h1 {
      font-size: var(--text-xl); // 增大字体
      font-weight: var(--font-bold); // 增加字重
      color: var(--text-primary);
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }
  }

  .favorite-btn {
    width: var(--space-11); // 44px - iOS触摸目标标准
    height: var(--space-11);
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);

    svg {
      width: 20px;
      height: 20px;
      color: var(--text-secondary);
      stroke-width: 2;
    }

    &:hover {
      background: var(--accent-500);

      svg {
        color: var(--text-on-accent);
      }
    }

    &:active {
      transform: scale(0.95);
    }

    // 收藏状态
    &.favorited {
      background: var(--accent-500);

      svg {
        color: var(--text-on-accent);
      }
    }
  }
}

// 可滚动内容区域 - iOS优化版本
.exercise-detail-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--space-4);
  padding-top: calc(env(safe-area-inset-top) + var(--space-11) + var(--space-8)); // 为固定头部预留空间
  padding-bottom: calc(env(safe-area-inset-bottom) + var(--space-8));

  // iOS滚动优化
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
  scroll-behavior: smooth;

  // 确保内容可以完全滚动
  min-height: max-content;

  // 各个部分的间距
  > * + * {
    margin-top: var(--space-6);
  }
}

// 动作标题部分
.exercise-title-section {
  text-align: center;
  
  .exercise-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  .exercise-meta {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-3);
    margin-top: var(--space-2);
    
    .meta-item {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      background: var(--bg-secondary);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-sm);
      
      @media (max-width: 768px) {
        font-size: var(--text-xs);
      }
    }
  }
}

// 动作信息区域
.exercise-info-section {
  .exercise-info-card {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    
    .exercise-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--space-3);
      
      @media (max-width: 768px) {
        flex-wrap: wrap;
        gap: var(--space-2);
      }
      
      .meta-item {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        
        @media (max-width: 768px) {
          font-size: var(--text-xs);
        }
      }
      
      .difficulty {
        display: flex;
        align-items: center;
        
        .difficulty-plate {
          width: 20px;
          height: 20px;
          object-fit: contain;
          vertical-align: middle;
        }
      }
    }
  }
}

// 视频播放区域
.video-section {
  .video-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    
    .exercise-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: var(--radius-lg);
    }
    
    // 视频控制覆盖层
    .video-controls-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity var(--transition-normal) var(--ease-in-out);
      pointer-events: none;
      
      .play-pause-btn {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.9);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        backdrop-filter: blur(4px);
        
        svg {
          width: 24px;
          height: 24px;
          color: var(--text-primary);
          stroke-width: 2;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.1);
        }
        
        &.paused {
          svg {
            margin-left: 2px; // 播放图标向右偏移一点
          }
        }
      }
    }
    
    // 悬停时显示控制按钮
    &:hover .video-controls-overlay {
      opacity: 1;
      pointer-events: auto;
    }
    
    // 移动设备上点击时显示控制按钮
    &:active .video-controls-overlay {
      opacity: 1;
      pointer-events: auto;
    }
  }
}

// Tab 切换区域 - iOS优化版本
.exercise-tabs-section {
  margin-top: var(--space-6);

  .exercise-tabs {
    // HeroUI Tabs 自定义样式
    --heroui-primary: var(--accent-500);
    --heroui-primary-foreground: var(--text-on-accent);

    // Tab 头部样式
    [role="tablist"] {
      border-bottom: 2px solid var(--accent-500); // 增加边框厚度和颜色，使效果更明显
      margin-bottom: var(--space-4);
      background: rgba(var(--accent-500), 0.05); // 添加背景色，使效果更明显

      // Tab 按钮样式
      [role="tab"] {
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        color: var(--text-secondary);
        padding: var(--space-3) var(--space-4);
        min-height: var(--space-11); // iOS触摸目标标准
        transition: all var(--transition-normal) var(--ease-in-out);

        &[aria-selected="true"] {
          color: var(--accent-500);
          font-weight: var(--font-semibold);
          background: rgba(var(--accent-500), 0.1); // 添加选中状态背景
          border-radius: var(--radius-md); // 添加圆角
        }

        &:hover {
          color: var(--text-primary);
        }

        // iOS触摸反馈
        &:active {
          transform: scale(0.98);
        }
      }
    }

    // Tab 内容区域
    [role="tabpanel"] {
      padding-top: var(--space-2);
      animation: fadeIn var(--transition-normal) var(--ease-in-out);
    }

    // Tab 内容包装器样式
    .tab-content-wrapper {
      padding: var(--space-4) 0;
      animation: slideInUp 0.3s ease-out;
      border-left: 3px solid var(--accent-500); // 添加左边框，使效果更明显
      padding-left: var(--space-4);
      background: rgba(var(--accent-500), 0.02); // 添加背景色
      border-radius: var(--radius-lg);
    }
  }
}

// 淡入动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 滑入动画 - 更明显的效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 训练部位信息区域 - 移除标题（现在在Tab中）
.muscle-info-section {
  // 移除原有的h2标题样式，现在由Tab标题处理

  // 整体卡片容器 - 严格的左右布局
  .muscle-info-card {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
    min-height: 320px;
    overflow: hidden; // 防止内容溢出

    // iOS设备保持左右布局，不改为上下布局
    @media (max-width: 768px) {
      gap: var(--space-3);
      min-height: 280px;
      padding: var(--space-3);
    }

    // 只有极小屏幕（iPhone SE等）才考虑上下布局
    @media (max-width: 375px) {
      flex-direction: column;
      align-items: stretch;
      gap: var(--space-4);
      min-height: auto;
    }
  }

  // 左侧肌肉列表边栏（严格20%宽度）
  .muscle-list-sidebar {
    flex: 0 0 20%;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    min-width: 100px;

    // iOS设备优化 - 保持20%比例
    @media (max-width: 768px) {
      flex: 0 0 20%;
      min-width: 80px;
    }

    // 只有极小屏幕才改为100%宽度
    @media (max-width: 375px) {
      flex: none;
      width: 100%;
      min-width: auto;
    }

    .muscle-category {
      margin-bottom: var(--space-3);

      h3 {
        font-size: var(--text-sm);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2) 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        position: relative;
        padding-bottom: var(--space-2);
        display: inline-block;
        width: auto;

        // 使用CSS变量的下划线
        background-image: linear-gradient(transparent, transparent);
        background-repeat: no-repeat;
        background-size: 100% 3px;
        background-position: 0 calc(100% - 2px);

        // iOS小屏幕优化
        @media (max-width: 768px) {
          font-size: var(--text-xs);
          margin: 0 0 var(--space-1) 0;
          padding-bottom: var(--space-1);
        }
      }

      // 主要肌肉标题 - 使用设计系统颜色
      &:has(.muscle-tag.primary) h3 {
        background-image: linear-gradient(var(--accent-500), var(--accent-500));
      }

      // 次要肌肉标题 - 使用设计系统颜色
      &:has(.muscle-tag.secondary) h3 {
        background-image: linear-gradient(var(--accent-300), var(--accent-300));
      }

      .muscle-tags {
        display: flex;
        flex-direction: column;
        gap: var(--space-1);
        align-items: flex-start;

        .muscle-tag {
          display: inline-block;
          padding: var(--space-2) var(--space-3);
          border-radius: var(--radius-full);
          font-size: var(--text-sm); // 增大字体
          font-weight: var(--font-medium);
          color: var(--text-primary);
          background: var(--bg-secondary);
          border: none;
          text-align: center;
          white-space: nowrap;
          width: fit-content;
          max-width: 100%;
          transition: all var(--transition-normal) var(--ease-in-out);

          // iOS触摸反馈
          &:hover {
            background: var(--bg-hover);
            transform: translateY(-1px);
          }

          &:active {
            transform: scale(0.98);
          }

          // 统一样式，不区分primary和secondary的背景色
          &.primary,
          &.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
          }

          // iOS小屏幕优化
          @media (max-width: 768px) {
            padding: var(--space-2) var(--space-3); // 保持较大的padding
            font-size: var(--text-xs); // 小屏幕稍小但仍可读
          }
        }
      }
    }
  }

  // 右侧肌肉图容器（严格80%宽度）
  .muscle-illustration-container {
    flex: 0 0 80%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 280px;
    overflow: hidden; // 防止图片溢出

    // 确保肌肉图不会超出容器
    > * {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }

    // iOS设备保持80%比例
    @media (max-width: 768px) {
      flex: 0 0 80%;
      min-height: 240px;
    }

    // 只有极小屏幕才改为100%宽度
    @media (max-width: 375px) {
      flex: none;
      width: 100%;
      min-height: 200px;
    }

    .no-muscle-data {
      text-align: center;
      color: var(--text-secondary);

      p {
        margin: 0;
        font-size: var(--text-sm);
      }
    }
  }
}

// 增强肌肉可视化组件样式
.enhanced-muscle-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  
  .muscle-svg {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
  
  .muscle-legend {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    align-items: center;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      
      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-color);
      }
      
      span {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-medium);
      }
    }
  }
}

.enhanced-muscle-illustration-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

// 动作指导区域 - 移除标题（现在在Tab中）
.instructions-section {
  // 移除原有的h2标题样式，现在由Tab标题处理

  .instruction-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    
    .instruction-step {
      display: flex;
      gap: var(--space-3);
      padding: var(--space-4);
      background: var(--bg-surface);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        border-color: var(--accent-300);
        background: var(--bg-hover);
      }
      
      &.active {
        border-color: var(--accent-500);
        background: var(--accent-50);
      }
      
      .step-number {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        background: var(--accent-500);
        color: var(--text-on-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        flex-shrink: 0;
      }
      
      .step-content {
        flex: 1;
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.6;
      }
    }
  }
}

// 注意事项区域 - 移除标题（现在在Tab中）
.tips-section {
  // 移除原有的h2标题样式，现在由Tab标题处理

  .exercise-tips {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    
    .tip-item {
      display: flex;
      gap: var(--space-3);
      padding: var(--space-4);
      border-radius: var(--radius-lg);
      border-left: 4px solid;
      
      &.warning {
        background: var(--warning-50);
        border-left-color: var(--warning-500);
        
        .tip-icon svg {
          color: var(--warning-600);
        }
      }
      
      &.info {
        background: var(--info-50);
        border-left-color: var(--info-500);
        
        .tip-icon svg {
          color: var(--info-600);
        }
      }
      
      .tip-icon {
        flex-shrink: 0;
        
        svg {
          width: 20px;
          height: 20px;
          stroke-width: 2;
        }
      }
      
      .tip-content {
        flex: 1;
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.6;
      }
    }
  }
}



// 头部占位符（用于Loading和Error状态）
.header-placeholder {
  width: var(--space-11); // 44px - 与按钮保持一致
  height: var(--space-11);
}

// Loading和Error状态 - iOS优化版本
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px); // 减去头部高度
  padding: var(--space-8);
  text-align: center;

  .loading-spinner, .error-icon {
    margin-bottom: var(--space-4);

    svg {
      width: 48px;
      height: 48px;
      color: var(--text-secondary);

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      &.spinning {
        animation: spin 1s linear infinite;
      }
    }
  }

  h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }

  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0 0 var(--space-4) 0;
    max-width: 300px;
    line-height: 1.5;
  }

  .error-actions {
    display: flex;
    gap: var(--space-3);

    .retry-btn, .back-btn-secondary {
      padding: var(--space-3) var(--space-6);
      border-radius: var(--radius-lg);
      border: none;
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      min-height: var(--space-11); // iOS触摸目标标准
    }

    .retry-btn {
      background: var(--accent-500);
      color: var(--text-on-accent);

      &:hover {
        background: var(--accent-400);
      }

      &:active {
        transform: scale(0.98);
      }
    }

    .back-btn-secondary {
      background: var(--bg-secondary);
      color: var(--text-primary);

      &:hover {
        background: var(--bg-hover);
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

// iOS响应式设计优化
@media (max-width: 768px) {
  .exercise-detail-page {
    // iOS设备特殊处理
    @supports (-webkit-touch-callout: none) {
      height: -webkit-fill-available;
    }
  }

  .exercise-detail-header {
    padding: var(--space-3);
    padding-top: calc(env(safe-area-inset-top) + var(--space-3));

    .back-btn,
    .favorite-btn {
      width: 40px;
      height: 40px;

      svg {
        width: 18px;
        height: 18px;
      }
    }

    .sticky-title h1 {
      font-size: var(--text-base);
    }
  }

  .exercise-detail-content {
    padding: var(--space-3);
    padding-top: calc(env(safe-area-inset-top) + 40px + var(--space-6));
  }

  // Tab 移动端优化
  .exercise-tabs-section {
    .exercise-tabs {
      [role="tablist"] {
        // 移动端Tab按钮优化
        [role="tab"] {
          font-size: var(--text-sm);
          padding: var(--space-2) var(--space-3);
          min-height: var(--space-11); // 保持iOS触摸目标标准
        }
      }
    }
  }
}

// 平板和桌面端优化
@media (min-width: 768px) {
  .exercise-detail-content {
    max-width: 800px;
    margin: 0 auto;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }
}

@media (min-width: 1024px) {
  .exercise-detail-content {
    max-width: 1000px;
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

// iOS暗色主题支持
.theme-dark {
  .exercise-detail-header {
    &.scrolled {
      background: rgba(var(--bg-primary-rgb, 15, 23, 42), 0.95);
    }
  }

  .muscle-info-card {
    background: var(--bg-surface);
    border-color: var(--border-color);
  }

  .muscle-tag {
    background: var(--bg-secondary) !important;
    color: var(--text-primary) !important;

    &:hover {
      background: var(--bg-hover) !important;
    }
  }

  // HeroUI Tabs 暗色主题
  .exercise-tabs {
    [role="tablist"] {
      border-bottom-color: var(--border-color);

      [role="tab"] {
        color: var(--text-secondary);

        &[aria-selected="true"] {
          color: var(--accent-400); // 暗色主题下使用稍浅的accent色
        }

        &:hover {
          color: var(--text-primary);
        }
      }
    }
  }
}

// iOS特有的滚动优化
@supports (-webkit-touch-callout: none) {
  .exercise-detail-content {
    // 启用iOS动量滚动
    -webkit-overflow-scrolling: touch;

    // 防止橡皮筋效果
    overscroll-behavior: none;

    // 优化滚动性能
    will-change: scroll-position;
  }
}